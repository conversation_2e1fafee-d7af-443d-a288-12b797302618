import type { ClientReadableStream, StatusObject } from '@grpc/grpc-js'
import { Emitter } from '@kdt310722/utils/event'
import type { Awaitable } from '@kdt310722/utils/promise'

export interface ResubscribeOptions {
    enabled?: boolean
    retries?: number
    delay?: number
    backoff?: number
    jitter?: number
    maxDelay?: number
    autoReset?: boolean
    circuitBreakerTimeout?: number
    shouldResubscribe?: (error?: unknown, status?: StatusObject) => boolean
}

export interface TimeoutOptions {
    subscribe?: number
    close?: number
    idle?: number // Max idle time between two message
}

export interface StreamWrapperOptions {
    timeout?: TimeoutOptions
    resubscribe?: ResubscribeOptions
    closeOnError?: boolean
    destroyOnCloseFail?: boolean
}

export type StreamWrapperEvents<TData> = {
    subscribed: () => void
    closed: (isExplicitly: boolean) => void
    data: (data: TData) => void
    closeError: (error: unknown) => void
    status: (status: StatusObject) => void
    pause: () => void
    resume: () => void
}

export class StreamWrapper<TResponse, TStream extends ClientReadableStream<TResponse> = ClientReadableStream<TResponse>> extends Emitter<StreamWrapperEvents<TResponse>> {
    public constructor(protected readonly subscriber: () => Awaitable<TStream>, options: StreamWrapperOptions = {}) {
        super()
    }

    public async subscribe() {}

    public async close(isExplicitly?: boolean, destroyOnCloseFail?: boolean) {}
}
