import { notNullish } from '@kdt310722/utils/common'
import { tap } from '@kdt310722/utils/function'
import { formatNanoseconds, isBigInt } from '@kdt310722/utils/number'
import { YellowstoneGeyserClient, createSubscribeRequest } from '../src/clients/yellowstone'

const getErrorMessage = (error: unknown) => {
    if (error instanceof Error) {
        return error.message + (notNullish(error.cause) ? `: ${getErrorMessage(error.cause)}` : '')
    }

    return error
}

const stringify = (data: unknown) => {
    return JSON.stringify(data, (_, value) => {
        if (isBigInt(value)) {
            return value.toString()
        }

        return value
    })
}

async function run() {
    const client = new YellowstoneGeyserClient('https://solana-yellowstone-grpc.publicnode.com')

    console.log('Version:', await client.getVersion({}))
    console.log('Blockhash:', await client.getLatestBlockhash({}))

    const stream = client.createStream({
        heartbeat: {
            interval: 10_000,
            timeout: 200,
        },
        resubscribe: {
            shouldResubscribe: (error, status) => tap(true, () => console.log('Resubscribe check:', getErrorMessage(error), status)),
        },
    })

    process.on('SIGINT', () => {
        console.log('SIGINT received, closing stream...')

        stream.close().catch((error) => {
            console.error('Error closing stream:', getErrorMessage(error))
            process.exit(1)
        })
    })

    // stream.on('data', (data) => console.log('Data received:', stringify(data)))
    stream.on('error', (error) => console.error(getErrorMessage(error)))
    stream.on('subscribed', () => console.log('Stream subscribed'))
    stream.on('closed', () => console.log('Stream closed'))
    stream.on('pause', () => console.log('Stream paused'))
    stream.on('resume', () => console.log('Stream resumed'))
    stream.on('status', (status) => console.log('Stream status:', status))
    stream.on('waitForResubscribe', (delay) => console.log(`Will resubscribe in ${formatNanoseconds(BigInt(delay * 1e6))}`))
    stream.on('resubscribe', (attempt, retriesLeft) => console.log('Stream resubscribe:', 'attempt', attempt, 'retriesLeft', retriesLeft))
    stream.on('circuitBreakerTripped', (lastResubscribeSuccessTime) => console.log('Circuit breaker tripped', new Date(lastResubscribeSuccessTime).toISOString()))
    stream.on('resubscribed', () => console.log('Stream resubscribed'))
    stream.on('write', (data) => console.log('Stream write:', stringify(data)))

    stream.on('resubscribeAbandoned', () => {
        console.log('Resubscribe Abandoned')
        process.exit(1)
    })

    stream.on('closeError', (error) => {
        console.log('closeError', getErrorMessage(error))
        process.exit(1)
    })

    await stream.subscribe()
    await stream.write(createSubscribeRequest({ blocksMeta: { block: {} } }))
}

run().catch((error) => {
    console.error(getErrorMessage(error))
    process.exit(1)
})
